<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载组件测试 - 失败状态</title>
    <style>
        /* 模拟 Ionic 模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .dialog-box-xs {
            --height: auto;
            --max-height: 70vh;
            --min-height: 200px;
            --width: 280px;
            height: var(--height);
            max-height: var(--max-height);
            min-height: var(--min-height);
            width: var(--width);
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        /* 复制下载组件的样式 */
        .download-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            max-height: 70vh;
            min-height: 200px;
            overflow: hidden;
            box-sizing: border-box;
        }

        .download-header {
            text-align: center;
            min-height: 42px;
            height: 42px;
            line-height: 42px;
            width: 100%;
            color: white;
            background-color: #3880ff;
            font-size: 16px;
            font-weight: 500;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            box-sizing: border-box;
        }

        .download-context {
            padding: 20px 16px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex: 1;
            min-height: 100px;
            background-color: #fff;
        }

        .error-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 16px 0;
        }

        .error-icon {
            font-size: 48px;
            color: #ff6b6b;
            margin-bottom: 12px;
        }

        .error-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            font-weight: 400;
        }

        .download-footer {
            border-top: 1px solid #f6f6f6;
            min-height: 42px;
            height: 42px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            flex-shrink: 0;
            padding: 0 16px;
            box-sizing: border-box;
        }

        .btn-copy {
            width: 100%;
            text-align: center;
            color: #ff6b6b;
            font-size: 14px;
            font-weight: 500;
            padding: 12px 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: transparent;
            gap: 6px;
        }

        .btn-copy:hover {
            background-color: rgba(255, 107, 107, 0.1);
        }

        .btn-copy:active {
            background-color: rgba(255, 107, 107, 0.2);
        }

        .test-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 2000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #3880ff;
            background: #3880ff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .test-controls button:hover {
            background: #3171e0;
        }

        /* 模拟 ion-icon */
        .ion-icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            vertical-align: middle;
        }

        .warning-icon::before {
            content: "⚠️";
        }

        .copy-icon::before {
            content: "📋";
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h3>下载组件测试</h3>
        <button onclick="showErrorState()">显示下载失败状态</button>
        <button onclick="hideModal()">关闭弹窗</button>
        <br>
        <small>测试下载失败时的新界面和复制功能</small>
    </div>
    
    <div id="modal" class="modal-backdrop" style="display: none;">
        <div class="dialog-box-xs">
            <div class="download-container">
                <div class="download-header">
                    版本更新中
                </div>
                <div class="download-context">
                    <!-- 下载失败时显示错误信息 -->
                    <div class="error-message">
                        <div class="error-icon warning-icon"></div>
                        <div class="error-text">
                            下载失败，网络连接可能不稳定。<br>
                            请点击下方按钮复制下载地址，<br>
                            在浏览器中手动下载安装包。
                        </div>
                    </div>
                </div>
                <div class="download-footer">
                    <!-- 下载失败 -->
                    <button class="btn-copy" onclick="copyDownloadUrl()">
                        <span class="ion-icon copy-icon"></span>
                        复制下载地址
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const testUrl = 'https://example.com/app-v1.2.3.apk';
        
        function showErrorState() {
            document.getElementById('modal').style.display = 'flex';
        }
        
        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        async function copyDownloadUrl() {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(testUrl);
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = testUrl;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }
                
                alert('下载地址已复制到剪贴板：\n' + testUrl);
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，下载地址为：\n' + testUrl);
            }
        }
        
        // 点击背景关闭弹窗
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
