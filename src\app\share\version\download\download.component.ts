import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FileOpener } from '@ionic-native/file-opener/ngx';
import { FileTransfer, FileTransferObject } from '@ionic-native/file-transfer/ngx';
import { File } from '@ionic-native/file/ngx';
import { ToastController } from '@ionic/angular';
import { from } from 'rxjs';

@Component({
  selector: 'app-download',
  templateUrl: './download.component.html',
  styleUrls: ['./download.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DownloadComponent implements OnInit {
  @Input() url: string;
  //  本地存储路径
  readonly localPath = this.file.dataDirectory + 'android.apk';
  /**
   * 0 下载中
   * 1 下载完成
   * 2 下载错误
   */
  state = 0;
  /**
   * 进度
   */
  progress = 0;

  constructor(
    private transfer: FileTransfer,
    private file: File,
    private fileOpener: <PERSON><PERSON>pen<PERSON>,
    private toastController: ToastController,
    private cd: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    if (this.url) {
      this.downloadApk();
    }
  }
  downloadApk(): void {
    const fileTransfer: FileTransferObject = this.transfer.create();
    from(fileTransfer.download(this.url, this.localPath, true)).subscribe(this.onDownloadSuccess, this.onDownloaderror);
    fileTransfer.onProgress((event: ProgressEvent) => {
      this.progress = Math.floor(event.loaded / event.total);
      this.cd.detectChanges(); // 触发变更检测
    });
  }

  /**
   * 更新完成
   */
  onDownloadSuccess = (_ret: any) => {
    this.state = 1;
    this.cd.detectChanges(); // 触发变更检测
  }

  /**
   * 失败
   */
  onDownloaderror = (_error: any) => {
    this.state = 2;
    this.cd.detectChanges(); // 触发变更检测
  }

  /**
   * 安装应用
   */
  async onInstallApp(): Promise<void> {
    await this.fileOpener.open(this.localPath, 'application/vnd.android.package-archive');
  }

  /**
   * 复制下载地址
   */
  async onCopyDownloadUrl(): Promise<void> {
    try {
      // 在 Cordova 环境中，优先使用传统方法，更可靠
      const textArea = document.createElement('textarea');
      textArea.value = this.url;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      textArea.remove();

      if (successful) {
        const toast = await this.toastController.create({
          message: '下载地址已复制到剪贴板，请在浏览器中粘贴下载',
          duration: 3000,
          position: 'bottom',
          color: 'success'
        });
        await toast.present();
      } else {
        throw new Error('复制命令执行失败');
      }
    } catch (error) {
      console.error('复制失败:', error);
      // 显示下载地址供用户手动复制
      const toast = await this.toastController.create({
        message: `复制失败，下载地址：${this.url}`,
        duration: 5000,
        position: 'bottom',
        color: 'warning',
        buttons: [
          {
            text: '关闭',
            role: 'cancel'
          }
        ]
      });
      await toast.present();
    }
  }

}
