import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FileOpener } from '@ionic-native/file-opener/ngx';
import { FileTransfer, FileTransferObject } from '@ionic-native/file-transfer/ngx';
import { File } from '@ionic-native/file/ngx';
import { ToastController } from '@ionic/angular';
import { from } from 'rxjs';

@Component({
  selector: 'app-download',
  templateUrl: './download.component.html',
  styleUrls: ['./download.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DownloadComponent implements OnInit {
  @Input() url: string;
  //  本地存储路径
  readonly localPath = this.file.dataDirectory + 'android.apk';
  /**
   * 0 下载中
   * 1 下载完成
   * 2 下载错误
   */
  state = 0;
  /**
   * 进度
   */
  progress = 0;

  constructor(
    private transfer: FileTransfer,
    private file: File,
    private fileOpener: <PERSON><PERSON>pen<PERSON>,
    private toastController: ToastController,
    private cd: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    if (this.url) {
      this.downloadApk();
    }
  }
  downloadApk(): void {
    const fileTransfer: FileTransferObject = this.transfer.create();
    from(fileTransfer.download(this.url, this.localPath, true)).subscribe(this.onDownloadSuccess, this.onDownloaderror);
    fileTransfer.onProgress((event: ProgressEvent) => {
      this.progress = Math.floor(event.loaded / event.total);
      this.cd.detectChanges(); // 触发变更检测
    });
  }

  /**
   * 更新完成
   */
  onDownloadSuccess = (_ret: any) => {
    this.state = 1;
    this.cd.detectChanges(); // 触发变更检测
  }

  /**
   * 失败
   */
  onDownloaderror = (_error: any) => {
    this.state = 2;
    this.cd.detectChanges(); // 触发变更检测
  }

  /**
   * 安装应用
   */
  async onInstallApp(): Promise<void> {
    await this.fileOpener.open(this.localPath, 'application/vnd.android.package-archive');
  }

  /**
   * 复制下载地址
   */
  async onCopyDownloadUrl(): Promise<void> {
    try {
      // 使用浏览器原生Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(this.url);
      } else {
        // 降级方案：使用传统的document.execCommand
        const textArea = document.createElement('textarea');
        textArea.value = this.url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      const toast = await this.toastController.create({
        message: '下载地址已复制到剪贴板，请在浏览器中粘贴下载',
        duration: 3000,
        position: 'bottom',
        color: 'success'
      });
      await toast.present();
    } catch (error) {
      console.error('复制失败:', error);
      const toast = await this.toastController.create({
        message: '复制失败，请手动复制下载地址',
        duration: 3000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();
    }
  }

}
